//
//  EAAICostController.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-09.
//  AI调用成本控制器 - 实现AI功能的成本控制和调用频率管理
//

import Foundation
import SwiftUI

/// AI调用优先级
enum EAAICallPriority: String, CaseIterable {
    case critical = "关键"      // 用户主动求助、危机干预
    case high = "高"           // 重大里程碑、习惯创建建议
    case medium = "中"         // 每日洞察、社区内容生成
    case low = "低"            // 日常提醒、基础统计
    
    var costMultiplier: Double {
        switch self {
        case .critical: return 1.0
        case .high: return 0.8
        case .medium: return 0.6
        case .low: return 0.4
        }
    }
}

/// AI调用场景
enum EAAIScenario: String, CaseIterable {
    case userActiveHelp = "用户主动求助"
    case crisisIntervention = "危机干预"
    case majorMilestone = "重大里程碑"
    case dailyInsight = "每日洞察"
    case habitCreationSuggestion = "习惯创建建议"
    case communityContentGeneration = "社区内容生成"
    case dailyReminder = "日常提醒"
    case basicStatistics = "基础统计"
    case quickEncouragement = "快速鼓励"
    
    var priority: EAAICallPriority {
        switch self {
        case .userActiveHelp, .crisisIntervention:
            return .critical
        case .majorMilestone, .habitCreationSuggestion:
            return .high
        case .dailyInsight, .communityContentGeneration:
            return .medium
        case .dailyReminder, .basicStatistics, .quickEncouragement:
            return .low
        }
    }
}

/// 用户等级
enum EAUserTier: String, CaseIterable {
    case free = "免费版"
    case pro = "专业版"
    case premium = "高级版"
}

/// AI调用成本控制器
/// 实现多层级成本控制策略，确保AI功能的可持续性和用户体验
@MainActor
class EAAICostController: ObservableObject {
    
    // MARK: - 发布属性
    
    @Published var dailyCost: Double = 0.0
    @Published var monthlyCost: Double = 0.0
    @Published var dailyCallCount: Int = 0
    @Published var monthlyCallCount: Int = 0
    
    // MARK: - 成本限制配置
    
    private let dailyCostLimit: Double = 10.0      // 每日10元限制
    private let monthlyCostLimit: Double = 200.0   // 每月200元限制
    private let dailyCallLimit: Int = 500          // 每日500次调用限制
    private let monthlyCallLimit: Int = 10000      // 每月10000次调用限制
    
    // MARK: - 成本统计
    
    private var costHistory: [Date: Double] = [:]
    private var callHistory: [Date: Int] = [:]
    private var lastResetDate: Date = Date()
    
    // MARK: - 初始化
    
    init() {
        loadCostData()
        resetDailyCountersIfNeeded()
    }
    
    // MARK: - 成本控制核心方法
    
    /// 检查是否允许AI调用
    /// - Parameter priority: 调用优先级
    /// - Returns: 是否允许调用
    func shouldAllowCall(priority: EAAICallPriority) async -> Bool {
        // 重置计数器（如果需要）
        resetDailyCountersIfNeeded()
        
        // 关键优先级调用总是允许（确保核心用户体验）
        if priority == .critical {
            return true
        }
        
        // 检查每日成本限制
        if dailyCost >= dailyCostLimit {
            print("🚫 AI调用被拒绝：已达到每日成本限制 ¥\(dailyCostLimit)")
            return false
        }
        
        // 检查每月成本限制
        if monthlyCost >= monthlyCostLimit {
            print("🚫 AI调用被拒绝：已达到每月成本限制 ¥\(monthlyCostLimit)")
            return false
        }
        
        // 检查每日调用频率限制
        if dailyCallCount >= dailyCallLimit {
            print("🚫 AI调用被拒绝：已达到每日调用次数限制 \(dailyCallLimit)次")
            return false
        }
        
        // 检查每月调用频率限制
        if monthlyCallCount >= monthlyCallLimit {
            print("🚫 AI调用被拒绝：已达到每月调用次数限制 \(monthlyCallLimit)次")
            return false
        }
        
        return true
    }
    
    /// 记录AI调用成本
    /// - Parameters:
    ///   - cost: 调用成本
    ///   - scenario: 调用场景
    func recordCost(_ cost: Double, for scenario: EAAIScenario) {
        let adjustedCost = cost * scenario.priority.costMultiplier
        
        dailyCost += adjustedCost
        monthlyCost += adjustedCost
        dailyCallCount += 1
        monthlyCallCount += 1
        
        // 记录到历史数据
        let today = Calendar.current.startOfDay(for: Date())
        costHistory[today, default: 0.0] += adjustedCost
        callHistory[today, default: 0] += 1
        
        // 保存数据
        saveCostData()
        
        // 记录详细的成本分析
        print("💰 AI调用成本记录:")
        print("   场景: \(scenario.rawValue)")
        print("   优先级: \(scenario.priority.rawValue)")
        print("   原始成本: ¥\(String(format: "%.4f", cost))")
        print("   调整后成本: ¥\(String(format: "%.4f", adjustedCost))")
        print("   今日累计: ¥\(String(format: "%.2f", dailyCost))/¥\(dailyCostLimit)")
        print("   本月累计: ¥\(String(format: "%.2f", monthlyCost))/¥\(monthlyCostLimit)")
    }
    
    // MARK: - 用户分级成本配额
    
    /// 获取用户等级对应的成本配额
    /// - Parameter userTier: 用户等级
    /// - Returns: 每日和每月成本配额
    func getCostQuota(for userTier: EAUserTier) -> (daily: Double, monthly: Double) {
        switch userTier {
        case .free:
            return (daily: 2.0, monthly: 20.0)
        case .pro:
            return (daily: 10.0, monthly: 200.0)
        case .premium:
            return (daily: 50.0, monthly: 1000.0)
        }
    }
    
    /// 获取用户等级对应的调用次数配额
    /// - Parameter userTier: 用户等级
    /// - Returns: 每日和每月调用次数配额
    func getCallQuota(for userTier: EAUserTier) -> (daily: Int, monthly: Int) {
        switch userTier {
        case .free:
            return (daily: 50, monthly: 500)
        case .pro:
            return (daily: 500, monthly: 10000)
        case .premium:
            return (daily: 2000, monthly: 50000)
        }
    }
    
    // MARK: - 统计和分析
    
    /// 获取成本使用率
    /// - Returns: 每日和每月成本使用率
    func getCostUsageRate() -> (daily: Double, monthly: Double) {
        let dailyRate = dailyCostLimit > 0 ? dailyCost / dailyCostLimit : 0.0
        let monthlyRate = monthlyCostLimit > 0 ? monthlyCost / monthlyCostLimit : 0.0
        return (daily: min(dailyRate, 1.0), monthly: min(monthlyRate, 1.0))
    }
    
    /// 获取调用次数使用率
    /// - Returns: 每日和每月调用次数使用率
    func getCallUsageRate() -> (daily: Double, monthly: Double) {
        let dailyRate = dailyCallLimit > 0 ? Double(dailyCallCount) / Double(dailyCallLimit) : 0.0
        let monthlyRate = monthlyCallLimit > 0 ? Double(monthlyCallCount) / Double(monthlyCallLimit) : 0.0
        return (daily: min(dailyRate, 1.0), monthly: min(monthlyRate, 1.0))
    }
    
    /// 获取成本历史数据
    /// - Parameter days: 获取最近多少天的数据
    /// - Returns: 成本历史数据
    func getCostHistory(days: Int = 30) -> [(date: Date, cost: Double)] {
        let calendar = Calendar.current
        let endDate = calendar.startOfDay(for: Date())
        let startDate = calendar.date(byAdding: .day, value: -days, to: endDate) ?? endDate
        
        var history: [(date: Date, cost: Double)] = []
        var currentDate = startDate
        
        while currentDate <= endDate {
            let cost = costHistory[currentDate] ?? 0.0
            history.append((date: currentDate, cost: cost))
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate) ?? currentDate
        }
        
        return history
    }
    
    // MARK: - 私有方法
    
    /// 重置每日计数器（如果需要）
    private func resetDailyCountersIfNeeded() {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let lastReset = calendar.startOfDay(for: lastResetDate)
        
        if today > lastReset {
            // 新的一天，重置每日计数器
            dailyCost = 0.0
            dailyCallCount = 0
            lastResetDate = Date()
            
            // 检查是否需要重置月度计数器
            if !calendar.isDate(today, equalTo: lastReset, toGranularity: .month) {
                monthlyCost = 0.0
                monthlyCallCount = 0
                
                // 清理过期的历史数据（保留90天）
                cleanupOldHistory()
            }
            
            saveCostData()
            print("🔄 AI成本控制器：计数器已重置")
        }
    }
    
    /// 清理过期的历史数据
    private func cleanupOldHistory() {
        let calendar = Calendar.current
        let cutoffDate = calendar.date(byAdding: .day, value: -90, to: Date()) ?? Date()
        
        costHistory = costHistory.filter { $0.key >= cutoffDate }
        callHistory = callHistory.filter { $0.key >= cutoffDate }
    }
    
    /// 保存成本数据到UserDefaults
    private func saveCostData() {
        let userDefaults = UserDefaults.standard
        userDefaults.set(dailyCost, forKey: "ai_daily_cost")
        userDefaults.set(monthlyCost, forKey: "ai_monthly_cost")
        userDefaults.set(dailyCallCount, forKey: "ai_daily_call_count")
        userDefaults.set(monthlyCallCount, forKey: "ai_monthly_call_count")
        userDefaults.set(lastResetDate, forKey: "ai_last_reset_date")
        
        // 保存历史数据
        if let costData = try? JSONEncoder().encode(costHistory) {
            userDefaults.set(costData, forKey: "ai_cost_history")
        }
        if let callData = try? JSONEncoder().encode(callHistory) {
            userDefaults.set(callData, forKey: "ai_call_history")
        }
    }
    
    /// 从UserDefaults加载成本数据
    private func loadCostData() {
        let userDefaults = UserDefaults.standard
        dailyCost = userDefaults.double(forKey: "ai_daily_cost")
        monthlyCost = userDefaults.double(forKey: "ai_monthly_cost")
        dailyCallCount = userDefaults.integer(forKey: "ai_daily_call_count")
        monthlyCallCount = userDefaults.integer(forKey: "ai_monthly_call_count")
        lastResetDate = userDefaults.object(forKey: "ai_last_reset_date") as? Date ?? Date()
        
        // 加载历史数据
        if let costData = userDefaults.data(forKey: "ai_cost_history"),
           let decodedCostHistory = try? JSONDecoder().decode([Date: Double].self, from: costData) {
            costHistory = decodedCostHistory
        }
        if let callData = userDefaults.data(forKey: "ai_call_history"),
           let decodedCallHistory = try? JSONDecoder().decode([Date: Int].self, from: callData) {
            callHistory = decodedCallHistory
        }
    }
}

// MARK: - 成本统计结构

/// AI成本统计信息
struct EAAICostStatistics {
    let dailyCost: Double
    let monthlyCost: Double
    let dailyCallCount: Int
    let monthlyCallCount: Int
    let dailyUsageRate: Double
    let monthlyUsageRate: Double
    let averageCostPerCall: Double
    
    init(controller: EAAICostController) {
        self.dailyCost = controller.dailyCost
        self.monthlyCost = controller.monthlyCost
        self.dailyCallCount = controller.dailyCallCount
        self.monthlyCallCount = controller.monthlyCallCount
        
        let usageRates = controller.getCostUsageRate()
        self.dailyUsageRate = usageRates.daily
        self.monthlyUsageRate = usageRates.monthly
        
        self.averageCostPerCall = controller.dailyCallCount > 0 ? 
            controller.dailyCost / Double(controller.dailyCallCount) : 0.0
    }
}
