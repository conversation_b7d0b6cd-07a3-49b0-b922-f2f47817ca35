//
//  EAAIIntegrationTest.swift
//  Evolve
//
//  Created by AI Assistant on 2025-01-09.
//  AI集成测试服务 - 验证AI功能的完整性和集成状态
//

import Foundation
import SwiftUI

/// AI集成测试服务
/// 用于验证AI功能的完整性，确保所有组件正确集成
@MainActor
class EAAIIntegrationTest: ObservableObject {
    
    // MARK: - 测试状态
    
    @Published var isRunning: Bool = false
    @Published var testResults: [EAAITestResult] = []
    @Published var overallStatus: EAAITestStatus = .notStarted
    
    // MARK: - 依赖注入
    
    private let repositoryContainer: EARepositoryContainer
    private let aiDataBridge: EACommunityAIDataBridge
    private let costController: EAAICostController
    private let cacheManager: EAAICacheManager
    private let enhancementService: EAAIEnhancementService
    
    // MARK: - 初始化
    
    init(repositoryContainer: EARepositoryContainer) {
        self.repositoryContainer = repositoryContainer
        self.aiDataBridge = EACommunityAIDataBridge(repositoryContainer: repositoryContainer)
        self.costController = EAAICostController()
        self.cacheManager = EAAICacheManager()
        self.enhancementService = EAAIEnhancementService(
            repositoryContainer: repositoryContainer,
            aiDataBridge: aiDataBridge,
            cacheManager: cacheManager
        )
    }
    
    // MARK: - 主要测试方法
    
    /// 运行完整的AI集成测试
    func runFullIntegrationTest() async {
        isRunning = true
        testResults.removeAll()
        overallStatus = .running
        
        // 测试1：Repository容器集成
        await testRepositoryContainerIntegration()
        
        // 测试2：AI数据桥接服务
        await testAIDataBridgeIntegration()
        
        // 测试3：AI成本控制器
        await testAICostControllerIntegration()
        
        // 测试4：AI缓存管理器
        await testAICacheManagerIntegration()
        
        // 测试5：AI增强服务集成
        await testAIEnhancementServiceIntegration()
        
        // 测试6：依赖注入完整性
        await testDependencyInjectionIntegrity()
        
        // 计算总体结果
        calculateOverallStatus()
        isRunning = false
    }
    
    // MARK: - 具体测试方法
    
    /// 测试Repository容器集成
    private func testRepositoryContainerIntegration() async {
        let testName = "Repository容器集成测试"
        
        do {
            // 测试获取当前用户
            let currentUser = try await repositoryContainer.getCurrentUser()
            
            // 测试AI数据摘要方法
            if let user = currentUser {
                let habitSummary = try await repositoryContainer.getUserHabitSummary(userId: user.id)
                let communitySummary = try await repositoryContainer.getUserCommunityActivitySummary(userId: user.id)
                
                let success = habitSummary != nil && communitySummary != nil
                addTestResult(name: testName, success: success, message: success ? "Repository容器AI接口正常" : "AI数据摘要获取失败")
            } else {
                addTestResult(name: testName, success: true, message: "无当前用户，跳过测试")
            }
            
        } catch {
            addTestResult(name: testName, success: false, message: "Repository容器测试失败：\(error.localizedDescription)")
        }
    }
    
    /// 测试AI数据桥接服务
    private func testAIDataBridgeIntegration() async {
        let testName = "AI数据桥接服务测试"
        
        do {
            // 创建测试用户ID
            let testUserId = UUID()
            
            // 测试获取用户社交摘要
            let socialSummary = try await aiDataBridge.getUserSocialSummary(userId: testUserId)
            
            // 测试获取推荐上下文
            let recommendationContext = try await aiDataBridge.getContentRecommendationData(userId: testUserId)
            
            let success = socialSummary.userId == testUserId && recommendationContext.userId == testUserId
            addTestResult(name: testName, success: success, message: success ? "AI数据桥接服务正常" : "数据桥接测试失败")
            
        } catch {
            addTestResult(name: testName, success: false, message: "AI数据桥接测试失败：\(error.localizedDescription)")
        }
    }
    
    /// 测试AI成本控制器
    private func testAICostControllerIntegration() async {
        let testName = "AI成本控制器测试"
        
        // 测试成本控制逻辑
        let canCallCritical = await costController.shouldAllowCall(priority: .critical)
        let canCallLow = await costController.shouldAllowCall(priority: .low)
        
        // 测试成本记录
        costController.recordCost(0.01, for: .quickEncouragement)
        
        // 测试统计功能
        let usageRate = costController.getCostUsageRate()
        let costHistory = costController.getCostHistory(days: 7)
        
        let success = canCallCritical && usageRate.daily >= 0.0 && costHistory.count >= 0
        addTestResult(name: testName, success: success, message: success ? "AI成本控制器正常" : "成本控制器测试失败")
    }
    
    /// 测试AI缓存管理器
    private func testAICacheManagerIntegration() async {
        let testName = "AI缓存管理器测试"
        
        let testUserId = UUID()
        
        // 测试用户画像缓存
        let testProfile = EAAIUserProfile.empty(userId: testUserId)
        cacheManager.cacheUserProfile(testProfile, for: testUserId)
        let cachedProfile = cacheManager.getCachedUserProfile(userId: testUserId)
        
        // 测试AI洞察缓存
        let testInsight = EAAIInsight(
            id: UUID(),
            userId: testUserId,
            insightType: "test",
            content: "测试洞察",
            confidence: 0.8,
            generatedAt: Date(),
            expiresAt: Date().addingTimeInterval(3600),
            metadata: [:]
        )
        let insightKey = cacheManager.generateAIInsightKey(userId: testUserId, insightType: "test")
        cacheManager.cacheAIInsight(testInsight, key: insightKey)
        let cachedInsight = cacheManager.getCachedAIInsight(key: insightKey)
        
        // 测试统计功能
        let statistics = cacheManager.getCacheUsageStatistics()
        
        let success = cachedProfile?.userId == testUserId && 
                     cachedInsight?.userId == testUserId && 
                     statistics.totalCacheSize >= 0
        addTestResult(name: testName, success: success, message: success ? "AI缓存管理器正常" : "缓存管理器测试失败")
    }
    
    /// 测试AI增强服务集成
    private func testAIEnhancementServiceIntegration() async {
        let testName = "AI增强服务集成测试"
        
        let testUserId = UUID()
        
        // 测试AI增强功能套件
        let enhancementSuite = await enhancementService.getAIEnhancementSuite(for: testUserId)
        
        // 测试统计功能
        let statistics = enhancementService.getEnhancementStatistics()
        
        // 测试功能控制
        enhancementService.enableAIEnhancement()
        let isEnabled = enhancementService.isAIEnabled
        
        let success = enhancementSuite?.userId == testUserId && statistics.getCacheHitRate() >= 0.0 && isEnabled
        addTestResult(name: testName, success: success, message: success ? "AI增强服务正常" : "增强服务测试失败")
    }
    
    /// 测试依赖注入完整性
    private func testDependencyInjectionIntegrity() async {
        let testName = "依赖注入完整性测试"
        
        // 检查所有依赖是否正确注入
        let repositoryContainerValid = repositoryContainer.userRepository != nil
        let aiDataBridgeValid = aiDataBridge != nil
        let costControllerValid = costController != nil
        let cacheManagerValid = cacheManager != nil
        let enhancementServiceValid = enhancementService != nil
        
        let success = repositoryContainerValid && aiDataBridgeValid && costControllerValid && cacheManagerValid && enhancementServiceValid
        addTestResult(name: testName, success: success, message: success ? "依赖注入完整性正常" : "存在依赖注入问题")
    }
    
    // MARK: - 辅助方法
    
    /// 添加测试结果
    private func addTestResult(name: String, success: Bool, message: String) {
        let result = EAAITestResult(
            testName: name,
            success: success,
            message: message,
            timestamp: Date()
        )
        testResults.append(result)
        
        print("🧪 AI集成测试: \(name) - \(success ? "✅ 通过" : "❌ 失败") - \(message)")
    }
    
    /// 计算总体测试状态
    private func calculateOverallStatus() {
        let totalTests = testResults.count
        let passedTests = testResults.filter { $0.success }.count
        
        if totalTests == 0 {
            overallStatus = .notStarted
        } else if passedTests == totalTests {
            overallStatus = .allPassed
        } else if passedTests > 0 {
            overallStatus = .partiallyPassed
        } else {
            overallStatus = .allFailed
        }
        
        print("🎯 AI集成测试完成: \(passedTests)/\(totalTests) 通过 - 状态: \(overallStatus)")
    }
    
    /// 获取测试摘要
    func getTestSummary() -> String {
        let totalTests = testResults.count
        let passedTests = testResults.filter { $0.success }.count
        let failedTests = totalTests - passedTests
        
        return """
        AI集成测试摘要：
        - 总测试数：\(totalTests)
        - 通过：\(passedTests)
        - 失败：\(failedTests)
        - 成功率：\(totalTests > 0 ? String(format: "%.1f", Double(passedTests) / Double(totalTests) * 100) : "0.0")%
        - 状态：\(overallStatus.description)
        """
    }
}

// MARK: - 测试数据模型

/// AI测试结果
struct EAAITestResult {
    let testName: String
    let success: Bool
    let message: String
    let timestamp: Date
}

/// AI测试状态
enum EAAITestStatus {
    case notStarted
    case running
    case allPassed
    case partiallyPassed
    case allFailed
    
    var description: String {
        switch self {
        case .notStarted: return "未开始"
        case .running: return "运行中"
        case .allPassed: return "全部通过"
        case .partiallyPassed: return "部分通过"
        case .allFailed: return "全部失败"
        }
    }
}
